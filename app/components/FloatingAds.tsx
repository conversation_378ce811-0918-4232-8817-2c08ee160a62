import React, { useState, useEffect, useRef } from 'react';
import { ExternalLink } from 'lucide-react';

interface FloatingAdProps {
  id: string;
  title: string;
  description: string;
  buttonText: string;
  buttonUrl: string;
  icon: string;
  bgColor: string;
  textColor: string;
  delay: number;
  startPosition: { x: number; y: number };
}

const FloatingAd: React.FC<FloatingAdProps> = ({
  id,
  title,
  description,
  buttonText,
  buttonUrl,
  icon,
  bgColor,
  textColor,
  delay,
  startPosition
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState(startPosition);
  const [isHovered, setIsHovered] = useState(false);

  // 使用useRef保持位置和速度状态，避免重新初始化
  const currentPosRef = useRef({ ...startPosition });
  const velocityRef = useRef({
    x: (Math.random() - 0.5) * 3,
    y: (Math.random() - 0.5) * 3
  });
  const isHoveredRef = useRef(isHovered);

  useEffect(() => {
    // 延迟显示
    const showTimer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    // 移除自动隐藏逻辑，让广告持续飞行
    return () => clearTimeout(showTimer);
  }, [delay]);

  // 同步isHovered状态到ref
  useEffect(() => {
    isHoveredRef.current = isHovered;
  }, [isHovered]);

  useEffect(() => {
    if (!isVisible) return;

    let animationId: number;

    // 连续飞行动画 - 鼠标悬停时暂停
    const animate = () => {
      // 只有在非悬停状态下才更新位置
      if (!isHoveredRef.current) {
        // 更新位置
        currentPosRef.current.x += velocityRef.current.x;
        currentPosRef.current.y += velocityRef.current.y;

        // 边界检测和反弹
        const margin = 100;
        if (currentPosRef.current.x <= margin || currentPosRef.current.x >= window.innerWidth - margin) {
          velocityRef.current.x = -velocityRef.current.x;
          currentPosRef.current.x = Math.max(margin, Math.min(window.innerWidth - margin, currentPosRef.current.x));
        }
        if (currentPosRef.current.y <= margin || currentPosRef.current.y >= window.innerHeight - margin) {
          velocityRef.current.y = -velocityRef.current.y;
          currentPosRef.current.y = Math.max(margin, Math.min(window.innerHeight - margin, currentPosRef.current.y));
        }

        // 随机改变方向，让飞行更有趣
        if (Math.random() < 0.003) { // 0.3% 概率改变方向
          velocityRef.current.x += (Math.random() - 0.5) * 1;
          velocityRef.current.y += (Math.random() - 0.5) * 1;
        }

        // 限制速度范围
        const minSpeed = 1;
        const maxSpeed = 3;
        const speed = Math.sqrt(velocityRef.current.x * velocityRef.current.x + velocityRef.current.y * velocityRef.current.y);
        if (speed > maxSpeed) {
          velocityRef.current.x = (velocityRef.current.x / speed) * maxSpeed;
          velocityRef.current.y = (velocityRef.current.y / speed) * maxSpeed;
        } else if (speed < minSpeed) {
          velocityRef.current.x = (velocityRef.current.x / speed) * minSpeed;
          velocityRef.current.y = (velocityRef.current.y / speed) * minSpeed;
        }

        setPosition({ ...currentPosRef.current });
      }

      animationId = requestAnimationFrame(animate);
    };

    // 开始动画
    animationId = requestAnimationFrame(animate);

    // 清理函数
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [isVisible]); // 移除isHovered依赖

  const handleClick = () => {
    window.open(buttonUrl, '_blank', 'noopener,noreferrer');
  };

  if (!isVisible) return null;

  return (
    <div
      className={`fixed z-40 ${bgColor} rounded-xl shadow-2xl border border-white/20 p-3 cursor-pointer group hover:scale-105 max-w-xs backdrop-blur-sm ${isHovered ? 'ring-2 ring-white/50' : ''}`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translate(-50%, -50%)',
        minWidth: '200px',
        maxWidth: '280px',
        transition: 'transform 0.1s ease-out, box-shadow 0.2s ease-out',
        willChange: 'transform',
        boxShadow: isHovered ? '0 25px 50px -12px rgba(0, 0, 0, 0.5)' : undefined
      }}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 图标 */}
      <div className="flex items-center gap-3 mb-3">
        <div className="text-2xl">{icon}</div>
        <div className="flex-1">
          <h3 className={`${textColor} font-bold text-sm leading-tight flex items-center gap-2`}>
            {title}
            {isHovered && (
              <span className="text-xs opacity-75 animate-pulse">⏸️</span>
            )}
          </h3>
        </div>
      </div>

      {/* 描述 */}
      <p className={`${textColor} text-xs leading-relaxed mb-3 opacity-90`}>
        {description}
      </p>

      {/* 按钮 */}
      <div className={`inline-flex items-center gap-1 px-3 py-2 bg-white/20 hover:bg-white/30 ${textColor} rounded-lg transition-all text-xs font-medium group-hover:bg-white/40`}>
        <span>{buttonText}</span>
        <ExternalLink className="w-3 h-3" />
      </div>

      {/* 脉冲效果 */}
      <div className="absolute -inset-1 bg-gradient-to-r from-white/20 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  );
};

interface FloatingAdsProps {
  enabled?: boolean;
}

export const FloatingAds: React.FC<FloatingAdsProps> = ({ enabled = true }) => {
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    // 获取窗口尺寸
    const updateWindowSize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    updateWindowSize();
    window.addEventListener('resize', updateWindowSize);

    return () => window.removeEventListener('resize', updateWindowSize);
  }, []);

  if (!enabled || windowSize.width < 1024) return null; // 只在大屏幕显示

  const floatingAds = [
    {
      id: 'floating-1',
      title: '🚀 Augment 账号平台',
      description: '专业账号管理，告别封禁烦恼！稳定可靠的 AI 账号服务',
      buttonText: '立即体验',
      buttonUrl: 'https://augment.159email.shop/',
      icon: '🤖',
      bgColor: 'bg-gradient-to-br from-blue-600 to-purple-600',
      textColor: 'text-white',
      delay: 1000,
      startPosition: { x: -300, y: windowSize.height * 0.2 }
    },
    {
      id: 'floating-2',
      title: '💎 额度充足账号',
      description: '高额度账号池，无需担心使用限制，让您的 AI 开发更流畅',
      buttonText: '查看详情',
      buttonUrl: 'https://augment.159email.shop/',
      icon: '💰',
      bgColor: 'bg-gradient-to-br from-emerald-500 to-teal-600',
      textColor: 'text-white',
      delay: 4000,
      startPosition: { x: windowSize.width + 300, y: windowSize.height * 0.7 }
    },
    {
      id: 'floating-3',
      title: '🛡️ 防封禁保障',
      description: '专业风控策略，多重保护机制，让您无需为账号封禁而感到烦恼',
      buttonText: '了解更多',
      buttonUrl: 'https://augment.159email.shop/',
      icon: '🔒',
      bgColor: 'bg-gradient-to-br from-orange-500 to-red-500',
      textColor: 'text-white',
      delay: 7000,
      startPosition: { x: windowSize.width * 0.8, y: -300 }
    },
    {
      id: 'floating-4',
      title: '⚡ 即时可用',
      description: '账号即开即用，无需等待，快速接入您的项目和开发流程',
      buttonText: '立即获取',
      buttonUrl: 'https://augment.159email.shop/',
      icon: '🚀',
      bgColor: 'bg-gradient-to-br from-indigo-500 to-purple-600',
      textColor: 'text-white',
      delay: 10000,
      startPosition: { x: windowSize.width * 0.3, y: windowSize.height + 300 }
    },
    {
      id: 'floating-5',
      title: '🌟 24/7 稳定服务',
      description: '全天候稳定运行，专业技术团队维护，确保服务持续可用',
      buttonText: '立即使用',
      buttonUrl: 'https://augment.159email.shop/',
      icon: '⭐',
      bgColor: 'bg-gradient-to-br from-pink-500 to-rose-600',
      textColor: 'text-white',
      delay: 13000,
      startPosition: { x: -300, y: windowSize.height * 0.5 }
    }
  ];

  return (
    <>
      {floatingAds.map((ad) => (
        <FloatingAd key={ad.id} {...ad} />
      ))}
    </>
  );
};

export default FloatingAds;
